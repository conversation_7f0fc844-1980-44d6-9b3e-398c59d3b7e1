{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "13398147953090499", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398147953090499", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "查找适用于Google Chrome的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Chrome 应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\scoop\\apps\\googlechrome\\current\\115.0.5790.171\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "13398147953091352", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398147953091352", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\scoop\\apps\\googlechrome\\current\\115.0.5790.171\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "neajdppkdcdipfabeoofebfddakdcjhd": {"active_permissions": {"api": ["systemPrivate", "ttsEngine"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "13398147953092868", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398147953092868", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["tts_extension.js"]}, "description": "Component extension providing speech via the Google network text-to-speech service.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GSbNUMGygqQTNDMFGIjZNcwXsHLzkNkHjWbuY37PbNdSDZ4VqlVjzbWqODSe+MjELdv5Keb51IdytnoGYXBMyqKmWpUrg+RnKvQ5ibWr4MW9pyIceOIdp9GrzC1WZGgTmZismYR3AjaIpufZ7xDdQQv+XrghPWCkdVqLN+qZDA1HU+DURznkMICiDDSH2sU0egm9UbWfS218bZqzKeQDiC3OnTPlaxcbJtKUuupIm5knjze3Wo9Ae9poTDMzKgchg0VlFCv3uqox+wlD8sjXBoyBCCK9HpImdVAF1a7jpdgiUHpPeV/26oYzM9/grltwNR3bzECQgSpyXp0eyoegwIDAQAB", "manifest_version": 2, "name": "Google Network Speech", "permissions": ["systemPrivate", "ttsEngine", "https://www.google.com/"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Users\\<USER>\\scoop\\apps\\googlechrome\\current\\115.0.5790.171\\resources\\network_speech_synthesis", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"active_permissions": {"api": ["desktopCapture", "processes", "webrtcAudioPrivate", "webrtcDesktopCapturePrivate", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["runtime.onConnectExternal"], "first_install_time": "13398147953092141", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "13398147953092141", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"matches": ["https://*.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["desktopCapture", "enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcAudioPrivate", "webrtcDesktopCapturePrivate", "webrtcLoggingPrivate"], "version": "1.3.21"}, "path": "C:\\Users\\<USER>\\scoop\\apps\\googlechrome\\current\\115.0.5790.171\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"browser": {"show_home_button": "9CA3E9324F80C144B0235E96B0EF12999ED42749038339E96C0AA6931697B216"}, "default_search_provider_data": {"template_url_data": "31918E82B04A79EAF0C6116EC9A8ADA6E36E3E3534B541E631FD294D27106D53"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "1F11CE6AF8ADA526F32DC9271187B7333DC70D2831898AB043806EED411DF9F8", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "DCB2357F11EB55CCE008DDF771C4F3D8FF3C905F34EA10F5EADA85F502CB0B13", "neajdppkdcdipfabeoofebfddakdcjhd": "9051CE38CA45361E9EDA182C85A1430E3C6FDB8F4FF3FD979B73D07488CCFA8D", "nkeimhogjdpnpccoofpliimaahmaaome": "10493C038A562AF821665091FF5A4291E0386BA1F2CACCA25D6153B1B6E3F9D9"}}, "google": {"services": {"account_id": "3B07C43F4C480FA3546BBE3BE5E20FB8E845A3FC71AAE8EF5964E134DFB32A6F", "last_account_id": "F14D9B2DCD7204684352796C80B393F5321B2414638FF2D97ABE267345643A4E", "last_username": "DA91D785E1A9E954CC4DBF666799B34E18DC218615F6ADAE9E27F1B5C06C1BB2"}}, "homepage": "A11F3C0537306804D2E4716960EC7FDDF5E91578AED83B5523756766EB8CDFC3", "homepage_is_newtabpage": "F261BA592561B88E9FA86A59D9E3E5CE9AF80046A669F14D94052E6BB525E0D2", "media": {"cdm": {"origin_data": "8A25970278850F6F9500805967562FBF92E1F7B95E8A63D9CE8D77916006996F"}, "storage_id_salt": "3AAE4B8183E602699D395A6FA72B9CF5A33FAB0C1031BD67D35FB6182E501E85"}, "module_blocklist_cache_md5_digest": "71D8AB173E067C2B4EB87B4A561D62F344C4A31A7DF29D03133E8FFCEE8F2624", "pinned_tabs": "37A86BBBCD6255336CD3FEB0419C19B28219000DD0640D1F46B8AE7FC82CAA71", "prefs": {"preference_reset_time": "B51672514241043A3FAA8DF392E6A44DBFB914FD80C92F9618D18233E522282E"}, "safebrowsing": {"incidents_sent": "A9522805F11C6A32CF4328A8087BB4B322CE9B72859CCBE353218DE12A74F887"}, "search_provider_overrides": "5D6B1A187BD69C30732922C7BBF233B00BD234F71D719C6B1D3DC837BB29E1EC", "session": {"restore_on_startup": "3B1B3331516FFCB69D5910FAF1E21330BAD3B549B2FE1373DBFC3593CB1B5459", "startup_urls": "D43D48281FB8CB009FE0535D82C87E2786028C410E041B12ABA6D4B9AF71C3EC"}, "settings_reset_prompt": {"last_triggered_for_default_search": "AB860C84A9B25D4E8098DCB563F65B5B402F7811A91207D26EB1371FE7F9C3F4", "last_triggered_for_homepage": "203E3041D52DEC7242C6E4E54504A2FA8485A136E7E09E51A295C752EB86BDED", "last_triggered_for_startup_urls": "37B23951DF18465D759D41C1D1CB869A5A83232ADF80EB52F5B7ACDBEEB9811F", "prompt_wave": "C003147DA51B2E66FB3900A2F3A5A3DA85F59F0B9C78413F0499B65F4FE79008"}, "software_reporter": {"prompt_seed": "05871E4804B3532094C2AD06059B1CFF94B248B857A9A18E9DEF51551BCAC196", "prompt_version": "7489C25BF080C89A941CA412B63B4F18948A313506B25A7B8AB477960BCE19A3", "reporting": "28D14C0DB0A9CBEF3D5F8D1AE4527D23772B65A18792368C7DA2353914CCE530"}}, "super_mac": "1E5E5F035E6EBB942F482E3A4598BACA4617F69510A5E621BAD2895BBC56A626"}}